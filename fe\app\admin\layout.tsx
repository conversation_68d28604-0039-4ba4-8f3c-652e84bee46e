import { AppSidebar } from "@/components/app-sidebar"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Play, Square, Fullscreen, FerrisWheel } from 'lucide-react'

export default function AdminLayout({
  children,
}: Readonly<{
    children: React.ReactNode;
  }>) {

  return (
    <SidebarProvider>
      <SidebarInset className='h-screen w-full overflow-hidden'>
        <header className="border flex h-12 shrink-0 items-center gap-2 bg-white transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="w-full flex items-center justify-between gap-2 px-4">
            <div className="flex items-center gap-2">
              <FerrisWheel className="w-6 h-6 text-primary" />
              <div className="text-lg font-semibold text-gray-800">
                测试项目 A
              </div>
            </div>

            <div className='flex flex-row items-center gap-2'>
              <Select>
                <SelectTrigger className="w-46 rounded-none">
                  当前配置版本： 未命名
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="option1">Option 1</SelectItem>
                </SelectContent>
              </Select>

              <Button className='rounded-none' size='sm'>保存版本</Button>

              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />

              <Button className="ml-2 rounded-none" size='sm' variant="secondary">
                <Play className="w-4 h-4 mr-1" />
                开始采集
              </Button>

              <Button className="ml-2 rounded-none" size='sm'>
                <Square className="w-4 h-4 mr-1" />
                停止采集
              </Button>

              <Button className='rounded-none' size='sm'>
                <Fullscreen className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </header>

        <div className='flex w-full h-full overflow-hidden'>
          <AppSidebar />
          <main className="flex h-full w-full flex-col overflow-hidden rounded-sm bg-gray-100/50 p-2">
            { children }
          </main>
        </div>

      </SidebarInset>
    </SidebarProvider>
  );
}
